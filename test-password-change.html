<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تغيير كلمة السر - AL-SALAMAT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .feature-demo {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 اختبار ميزة تغيير كلمة سر الأدمن</h1>
        <p>هذا الاختبار يتحقق من عمل ميزة تغيير كلمة السر في لوحة الإدارة.</p>
        
        <div id="test-results">
            <div class="test-result info">
                <strong>📋 حالة الاختبار:</strong> جاري التحضير...
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 اختبارات تلقائية</h3>
            <button class="test-button" onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
            <button class="test-button" onclick="testFormElements()">📝 اختبار عناصر النموذج</button>
            <button class="test-button" onclick="testPasswordStrength()">💪 اختبار مؤشر القوة</button>
            <button class="test-button" onclick="testValidation()">✅ اختبار التحقق</button>
            <button class="test-button" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
        
        <div class="test-section">
            <h3>🎯 اختبار يدوي</h3>
            <p>للاختبار الكامل، يجب فتح لوحة الإدارة والذهاب إلى قسم "إدارة المستخدمين":</p>
            <button class="test-button" onclick="openAdminPanel()">🔗 فتح لوحة الإدارة</button>
            <div class="feature-demo">
                <h4>📍 خطوات الاختبار اليدوي:</h4>
                <ol>
                    <li>افتح لوحة الإدارة</li>
                    <li>سجل دخول كأدمن</li>
                    <li>اذهب إلى قسم "👥 إدارة المستخدمين"</li>
                    <li>ابحث عن قسم "🔐 تغيير كلمة سر الأدمن"</li>
                    <li>جرب تغيير كلمة السر</li>
                </ol>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 معلومات الميزة</h3>
            <div class="feature-demo">
                <h4>✨ المميزات المضافة:</h4>
                <ul>
                    <li>🔐 نموذج تغيير كلمة السر الآمن</li>
                    <li>💪 مؤشر قوة كلمة السر</li>
                    <li>✅ التحقق من تطابق كلمة السر</li>
                    <li>🛡️ التحقق من الصلاحيات</li>
                    <li>📝 تسجيل العمليات</li>
                </ul>
                
                <h4>🎨 التصميم:</h4>
                <ul>
                    <li>تصميم متناسق مع لوحة الإدارة</li>
                    <li>ألوان بصرية للتحقق</li>
                    <li>مؤشر قوة متدرج</li>
                    <li>رسائل خطأ واضحة</li>
                </ul>
            </div>
        </div>
        
        <iframe id="admin-frame" style="display: none;"></iframe>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = `
                <div class="test-result info">
                    <strong>📋 حالة الاختبار:</strong> تم مسح النتائج
                </div>
            `;
        }

        function runAllTests() {
            clearResults();
            addResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            
            setTimeout(() => testFormElements(), 500);
            setTimeout(() => testPasswordStrength(), 1500);
            setTimeout(() => testValidation(), 2500);
            setTimeout(() => testSecurity(), 3500);
            setTimeout(() => {
                addResult('✅ تم الانتهاء من جميع الاختبارات التلقائية', 'success');
                addResult('💡 للاختبار الكامل، افتح لوحة الإدارة واختبر الميزة يدوياً', 'warning');
            }, 4500);
        }

        function testFormElements() {
            addResult('📝 اختبار عناصر النموذج...', 'info');
            
            // Test if admin.html exists and contains the form
            fetch('admin.html')
                .then(response => response.text())
                .then(html => {
                    if (html.includes('change-password-form')) {
                        addResult('✅ تم العثور على نموذج تغيير كلمة السر', 'success');
                    } else {
                        addResult('❌ لم يتم العثور على نموذج تغيير كلمة السر', 'error');
                    }
                    
                    if (html.includes('current-password')) {
                        addResult('✅ حقل كلمة السر الحالية موجود', 'success');
                    } else {
                        addResult('❌ حقل كلمة السر الحالية مفقود', 'error');
                    }
                    
                    if (html.includes('new-password')) {
                        addResult('✅ حقل كلمة السر الجديدة موجود', 'success');
                    } else {
                        addResult('❌ حقل كلمة السر الجديدة مفقود', 'error');
                    }
                    
                    if (html.includes('confirm-password')) {
                        addResult('✅ حقل تأكيد كلمة السر موجود', 'success');
                    } else {
                        addResult('❌ حقل تأكيد كلمة السر مفقود', 'error');
                    }
                    
                    if (html.includes('password-strength')) {
                        addResult('✅ مؤشر قوة كلمة السر موجود', 'success');
                    } else {
                        addResult('❌ مؤشر قوة كلمة السر مفقود', 'error');
                    }
                })
                .catch(error => {
                    addResult('❌ خطأ في تحميل admin.html: ' + error.message, 'error');
                });
        }

        function testPasswordStrength() {
            addResult('💪 اختبار مؤشر قوة كلمة السر...', 'info');
            
            // Test CSS classes
            fetch('admin-styles.css')
                .then(response => response.text())
                .then(css => {
                    if (css.includes('strength-weak')) {
                        addResult('✅ تصميم كلمة السر الضعيفة موجود', 'success');
                    } else {
                        addResult('❌ تصميم كلمة السر الضعيفة مفقود', 'error');
                    }
                    
                    if (css.includes('strength-strong')) {
                        addResult('✅ تصميم كلمة السر القوية موجود', 'success');
                    } else {
                        addResult('❌ تصميم كلمة السر القوية مفقود', 'error');
                    }
                    
                    if (css.includes('password-strength')) {
                        addResult('✅ تصميم مؤشر القوة موجود', 'success');
                    } else {
                        addResult('❌ تصميم مؤشر القوة مفقود', 'error');
                    }
                })
                .catch(error => {
                    addResult('❌ خطأ في تحميل admin-styles.css: ' + error.message, 'error');
                });
        }

        function testValidation() {
            addResult('✅ اختبار وظائف التحقق...', 'info');
            
            // Test JavaScript functions
            fetch('admin-script.js')
                .then(response => response.text())
                .then(js => {
                    if (js.includes('changeAdminPassword')) {
                        addResult('✅ وظيفة تغيير كلمة السر موجودة', 'success');
                    } else {
                        addResult('❌ وظيفة تغيير كلمة السر مفقودة', 'error');
                    }
                    
                    if (js.includes('checkPasswordStrength')) {
                        addResult('✅ وظيفة فحص قوة كلمة السر موجودة', 'success');
                    } else {
                        addResult('❌ وظيفة فحص قوة كلمة السر مفقودة', 'error');
                    }
                    
                    if (js.includes('checkPasswordMatch')) {
                        addResult('✅ وظيفة فحص تطابق كلمة السر موجودة', 'success');
                    } else {
                        addResult('❌ وظيفة فحص تطابق كلمة السر مفقودة', 'error');
                    }
                    
                    if (js.includes('reauthenticateWithCredential')) {
                        addResult('✅ وظيفة إعادة المصادقة موجودة', 'success');
                    } else {
                        addResult('❌ وظيفة إعادة المصادقة مفقودة', 'error');
                    }
                })
                .catch(error => {
                    addResult('❌ خطأ في تحميل admin-script.js: ' + error.message, 'error');
                });
        }

        function testSecurity() {
            addResult('🛡️ اختبار الأمان...', 'info');
            
            fetch('admin-script.js')
                .then(response => response.text())
                .then(js => {
                    if (js.includes('verifyAdminAccess')) {
                        addResult('✅ التحقق من صلاحيات الأدمن موجود', 'success');
                    } else {
                        addResult('❌ التحقق من صلاحيات الأدمن مفقود', 'error');
                    }
                    
                    if (js.includes('adminLogs')) {
                        addResult('✅ تسجيل العمليات موجود', 'success');
                    } else {
                        addResult('❌ تسجيل العمليات مفقود', 'error');
                    }
                    
                    if (js.includes('updatePassword')) {
                        addResult('✅ وظيفة تحديث كلمة السر آمنة', 'success');
                    } else {
                        addResult('❌ وظيفة تحديث كلمة السر مفقودة', 'error');
                    }
                })
                .catch(error => {
                    addResult('❌ خطأ في فحص الأمان: ' + error.message, 'error');
                });
        }

        function openAdminPanel() {
            const iframe = document.getElementById('admin-frame');
            iframe.style.display = 'block';
            iframe.src = 'admin.html';
            addResult('🔗 تم فتح لوحة الإدارة في الإطار أدناه', 'info');
            addResult('💡 اذهب إلى قسم "إدارة المستخدمين" لاختبار الميزة', 'warning');
        }

        // Auto-run message when page loads
        window.onload = function() {
            addResult('🎯 مرحباً! اضغط على "تشغيل جميع الاختبارات" لبدء الفحص', 'info');
            addResult('📋 الملفات المطلوبة: admin.html, admin-styles.css, admin-script.js', 'info');
        };
    </script>
</body>
</html>
