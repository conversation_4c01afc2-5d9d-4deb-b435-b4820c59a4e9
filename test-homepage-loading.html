<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تحميل الصفحة الرئيسية - AL-SALAMAT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #0066cc;
        }
        .test-info ul {
            margin: 0;
            padding-right: 20px;
        }
        .test-info li {
            margin: 5px 0;
            color: #333;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار تحميل الصفحة الرئيسية المحدث</h1>
        <p>هذا الاختبار يتحقق من أن الصفحة الرئيسية تحمل فوراً مع المحتوى الافتراضي بدون انتظار Firebase.</p>
        <div class="test-info">
            <h3>✨ التحسينات الجديدة:</h3>
            <ul>
                <li>✅ عرض المحتوى الافتراضي فوراً</li>
                <li>✅ إخفاء مؤشرات التحميل</li>
                <li>✅ عرض 3 فروع افتراضية</li>
                <li>✅ عرض معلومات الاتصال مباشرة</li>
                <li>✅ عرض وصف الشركة فوراً</li>
            </ul>
        </div>
        
        <div id="test-results">
            <div class="test-result info">
                <strong>📋 حالة الاختبار:</strong> جاري التحضير...
            </div>
        </div>
        
        <div>
            <button class="test-button" onclick="runTests()">🚀 تشغيل الاختبارات</button>
            <button class="test-button" onclick="loadHomepage()">📄 تحميل الصفحة الرئيسية</button>
            <button class="test-button" onclick="checkContactLinks()">📞 اختبار روابط الاتصال</button>
            <button class="test-button" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
        
        <iframe id="homepage-frame" style="display: none;"></iframe>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = `
                <div class="test-result info">
                    <strong>📋 حالة الاختبار:</strong> تم مسح النتائج
                </div>
            `;
        }

        function runTests() {
            clearResults();
            addResult('🚀 بدء تشغيل الاختبارات...', 'info');
            
            // Test 1: Check if homepage loads
            setTimeout(() => {
                addResult('📄 اختبار 1: تحميل الصفحة الرئيسية...', 'info');
                loadHomepage();
            }, 500);
            
            // Test 2: Check content after loading
            setTimeout(() => {
                addResult('📊 اختبار 2: فحص المحتوى...', 'info');
                checkContent();
            }, 3000);
            
            // Test 3: Check contact links
            setTimeout(() => {
                addResult('📞 اختبار 3: فحص روابط الاتصال...', 'info');
                checkContactLinks();
            }, 4000);
        }

        function loadHomepage() {
            const iframe = document.getElementById('homepage-frame');
            iframe.style.display = 'block';
            iframe.src = 'index.html';
            
            iframe.onload = function() {
                addResult('✅ تم تحميل الصفحة الرئيسية بنجاح', 'success');
                
                // Check if content is visible
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        
                        // Check if branches section has content
                        const branchesGrid = iframeDoc.getElementById('dynamic-branches');
                        const branchCards = branchesGrid ? branchesGrid.querySelectorAll('.branch-card') : [];
                        const defaultBranches = branchesGrid ? branchesGrid.querySelectorAll('.default-branch') : [];

                        if (branchCards.length > 0) {
                            addResult(`✅ تم العثور على ${branchCards.length} فرع في الصفحة`, 'success');
                            if (defaultBranches.length > 0) {
                                addResult(`✅ تم العثور على ${defaultBranches.length} فرع افتراضي`, 'success');
                            }
                        } else {
                            addResult('⚠️ لم يتم العثور على فروع في الصفحة', 'error');
                        }
                        
                        // Check about section
                        const aboutDesc = iframeDoc.getElementById('about-description');
                        const aboutLoading = iframeDoc.getElementById('about-loading');
                        if (aboutDesc && aboutDesc.textContent.trim() !== '') {
                            addResult('✅ قسم "من نحن" يحتوي على محتوى', 'success');
                            if (aboutLoading && aboutLoading.style.display === 'none') {
                                addResult('✅ تم إخفاء مؤشر التحميل في قسم "من نحن"', 'success');
                            }
                        } else {
                            addResult('⚠️ قسم "من نحن" فارغ', 'error');
                        }
                        
                        // Check contact info
                        const phoneDisplay = iframeDoc.getElementById('contact-phone-display');
                        const emailDisplay = iframeDoc.getElementById('contact-email-display');
                        
                        if (phoneDisplay && phoneDisplay.textContent !== '######') {
                            addResult('✅ معلومات الهاتف متوفرة', 'success');
                        } else {
                            addResult('⚠️ معلومات الهاتف غير متوفرة', 'error');
                        }
                        
                        if (emailDisplay && emailDisplay.textContent !== '######') {
                            addResult('✅ معلومات البريد الإلكتروني متوفرة', 'success');
                        } else {
                            addResult('⚠️ معلومات البريد الإلكتروني غير متوفرة', 'error');
                        }
                        
                    } catch (error) {
                        addResult(`❌ خطأ في فحص المحتوى: ${error.message}`, 'error');
                    }
                }, 2000);
            };
            
            iframe.onerror = function() {
                addResult('❌ فشل في تحميل الصفحة الرئيسية', 'error');
            };
        }

        function checkContent() {
            const iframe = document.getElementById('homepage-frame');
            if (!iframe.src) {
                addResult('⚠️ يجب تحميل الصفحة أولاً', 'error');
                return;
            }
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Check if loading spinners are hidden
                const aboutLoading = iframeDoc.getElementById('about-loading');
                if (aboutLoading && aboutLoading.style.display === 'none') {
                    addResult('✅ تم إخفاء مؤشر التحميل في قسم "من نحن"', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل ما زال ظاهراً في قسم "من نحن"', 'error');
                }
                
                // Check if no-data message is hidden
                const noDataMessage = iframeDoc.getElementById('no-branches-message');
                if (noDataMessage && noDataMessage.style.display === 'none') {
                    addResult('✅ تم إخفاء رسالة "لا توجد فروع"', 'success');
                } else {
                    addResult('⚠️ رسالة "لا توجد فروع" ما زالت ظاهرة', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في فحص المحتوى: ${error.message}`, 'error');
            }
        }

        function checkContactLinks() {
            const iframe = document.getElementById('homepage-frame');
            if (!iframe.src) {
                addResult('⚠️ يجب تحميل الصفحة أولاً', 'error');
                return;
            }
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                const phoneLink = iframeDoc.getElementById('contact-phone-link');
                const emailLink = iframeDoc.getElementById('contact-email-link');
                
                if (phoneLink && phoneLink.href && phoneLink.href.startsWith('tel:')) {
                    addResult('✅ رابط الهاتف يعمل بشكل صحيح', 'success');
                } else {
                    addResult('❌ رابط الهاتف لا يعمل', 'error');
                }
                
                if (emailLink && emailLink.href && emailLink.href.startsWith('mailto:')) {
                    addResult('✅ رابط البريد الإلكتروني يعمل بشكل صحيح', 'success');
                } else {
                    addResult('❌ رابط البريد الإلكتروني لا يعمل', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في فحص روابط الاتصال: ${error.message}`, 'error');
            }
        }

        // Auto-run tests when page loads
        window.onload = function() {
            addResult('🎯 مرحباً! اضغط على "تشغيل الاختبارات" لبدء الفحص', 'info');
        };
    </script>
</body>
</html>
