// Homepage Authentication Manager for AL-SALAMAT
// Handles authentication for the main website to access Firebase data
// FIXED: Removed automatic demo account creation to prevent unwanted user registrations

class HomepageAuthManager {
    constructor() {
        this.auth = null;
        this.database = null;
        this.currentUser = null;
        this.isAuthenticated = false;
        this.retryCount = 0;
        this.maxRetries = 2; // Reduced retries to prevent excessive account creation attempts
        this.authAttempted = false; // Track if authentication was already attempted
        this.init();
    }

    async init() {
        try {
            console.log('🔐 Initializing Homepage Authentication...');

            // Wait for Firebase to be ready
            if (typeof firebase === 'undefined') {
                console.log('⏳ Waiting for Firebase...');
                setTimeout(() => this.init(), 1000);
                return;
            }

            this.auth = firebase.auth();
            this.database = firebase.database();

            // Set as authenticated to allow content loading
            console.log('🚀 Setting authenticated status - loading content from Firebase');
            this.isAuthenticated = true;
            this.onAuthSuccess();

            console.log('✅ Homepage Authentication initialized');
        } catch (error) {
            console.error('❌ Error initializing homepage auth:', error);
            // Even if there's an error, try to load content
            this.handleAuthFailure();
        }
    }

    async checkExistingAuth() {
        return new Promise((resolve) => {
            this.auth.onAuthStateChanged((user) => {
                if (user) {
                    this.currentUser = user;
                    this.isAuthenticated = true;
                    console.log('👤 User already authenticated:', user.uid);
                    this.onAuthSuccess();
                } else {
                    console.log('🔓 No existing authentication');
                    this.isAuthenticated = false;
                }
                resolve();
            });
        });
    }

    async authenticateAnonymously() {
        try {
            console.log('🔑 Attempting anonymous authentication...');

            const userCredential = await this.auth.signInAnonymously();
            this.currentUser = userCredential.user;
            this.isAuthenticated = true;

            console.log('✅ Anonymous authentication successful:', this.currentUser.uid);
            this.onAuthSuccess();

        } catch (error) {
            console.error('❌ Anonymous authentication failed:', error);

            // Instead of creating demo accounts, handle failure gracefully
            this.handleAuthFailure();
        }
    }

    onAuthSuccess() {
        console.log('🎉 Authentication successful, initializing content managers...');
        
        // Trigger content loading
        this.initializeContentManagers();
        
        // Show success indicator
        this.showAuthStatus(true);
    }

    handleAuthFailure() {
        console.error('💥 Authentication failed - showing content without real-time updates');
        this.showAuthStatus(false);

        // Show static content instead of creating demo accounts
        this.showStaticContent();
    }

    retryAuthentication() {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            console.log(`🔄 Retrying authentication (${this.retryCount}/${this.maxRetries})...`);
            setTimeout(() => this.authenticateAnonymously(), 2000 * this.retryCount);
        } else {
            this.handleAuthFailure();
        }
    }

    initializeContentManagers() {
        // Initialize dynamic content manager if available
        if (window.dynamicContentManager) {
            console.log('🔄 Reinitializing Dynamic Content Manager...');
            window.dynamicContentManager.loadAllContent();
        }

        // Initialize enhanced realtime manager if available
        if (window.enhancedRealtimeManager) {
            console.log('⚡ Reinitializing Enhanced Realtime Manager...');
            window.enhancedRealtimeManager.forceSync();
        }

        // Load content directly if managers are not available
        this.loadContentDirectly();
    }

    async loadContentDirectly() {
        try {
            console.log('📊 Loading content directly...');
            
            // Load company info
            await this.loadCompanyInfo();
            
            // Load branches
            await this.loadBranches();
            
            // Load contact info
            await this.loadContactInfo();
            
            // Load gallery
            await this.loadGallery();
            
            // Load site settings
            await this.loadSiteSettings();
            
            console.log('✅ Content loaded directly');
        } catch (error) {
            console.error('❌ Error loading content directly:', error);
        }
    }

    async loadCompanyInfo() {
        try {
            const snapshot = await this.database.ref('siteContent').once('value');
            const data = snapshot.val();
            
            if (data) {
                // Update company title
                const titleElement = document.getElementById('company-title');
                if (titleElement && data.title) {
                    titleElement.textContent = data.title;
                }
                
                // Update company subtitle
                const subtitleElement = document.getElementById('company-subtitle');
                if (subtitleElement && data.subtitle) {
                    subtitleElement.textContent = data.subtitle;
                }
                
                // Update company description
                const descriptionElement = document.getElementById('company-description');
                if (descriptionElement && data.description) {
                    descriptionElement.textContent = data.description;
                }
                
                console.log('✅ Company info loaded');
            }
        } catch (error) {
            console.error('❌ Error loading company info:', error);
        }
    }

    async loadBranches() {
        try {
            const snapshot = await this.database.ref('branches').once('value');
            const data = snapshot.val();

            const branchesGrid = document.getElementById('dynamic-branches');
            const noDataMessage = document.getElementById('no-branches-message');
            const branchesLoading = document.getElementById('branches-loading');

            if (!branchesGrid) return;

            // Hide loading spinner
            if (branchesLoading) {
                branchesLoading.style.display = 'none';
            }

            // Clear existing content (except loading and no-data elements)
            const existingBranches = branchesGrid.querySelectorAll('.branch-card');
            existingBranches.forEach(branch => branch.remove());

            if (data && Object.keys(data).length > 0) {
                // Hide no data message
                if (noDataMessage) {
                    noDataMessage.style.display = 'none';
                }

                // Add branches
                Object.entries(data).forEach(([branchId, branch]) => {
                    const branchCard = document.createElement('div');
                    branchCard.className = 'branch-card';
                    branchCard.innerHTML = `
                        <h3>${this.escapeHtml(branch.name || 'فرع غير محدد')}</h3>
                        <p><strong>العنوان:</strong> ${this.escapeHtml(branch.address || 'عنوان غير محدد')}</p>
                        ${branch.phone ? `<p><strong>الهاتف:</strong> ${this.escapeHtml(branch.phone)}</p>` : ''}
                        <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || branch.name)}"
                           target="_blank"
                           class="location-btn">
                           📍 الموقع
                        </a>
                    `;
                    branchesGrid.appendChild(branchCard);
                });

                console.log(`✅ Loaded ${Object.keys(data).length} branches`);
            } else {
                // Show no data message
                if (noDataMessage) {
                    noDataMessage.style.display = 'block';
                } else {
                    const noDataDiv = document.createElement('div');
                    noDataDiv.className = 'no-data-message';
                    noDataDiv.innerHTML = '<p>لا توجد فروع مضافة حالياً.</p>';
                    branchesGrid.appendChild(noDataDiv);
                }
                console.log('ℹ️ No branches data available');
            }
        } catch (error) {
            console.error('❌ Error loading branches:', error);
        }
    }

    async loadContactInfo() {
        try {
            const snapshot = await this.database.ref('contactSection').once('value');
            const data = snapshot.val();

            // Hide loading spinners
            const addressLoading = document.getElementById('address-loading');
            const hoursLoading = document.getElementById('hours-loading');

            if (addressLoading) {
                addressLoading.style.display = 'none';
            }
            if (hoursLoading) {
                hoursLoading.style.display = 'none';
            }

            if (data) {
                // Update contact elements
                const updates = [
                    { id: 'contact-title', value: data.title },
                    { id: 'contact-info-title', value: data.infoTitle },
                    { id: 'contact-address-display', value: data.address },
                    { id: 'contact-hours-display', value: data.hours }
                ];

                updates.forEach(update => {
                    const element = document.getElementById(update.id);
                    if (element && update.value) {
                        element.textContent = update.value;
                    }
                });

                console.log('✅ Contact info loaded');
            }
        } catch (error) {
            console.error('❌ Error loading contact info:', error);
        }
    }

    async loadGallery() {
        try {
            const snapshot = await this.database.ref('gallery').once('value');
            const data = snapshot.val();
            
            const galleryGrid = document.getElementById('dynamic-gallery');
            if (!galleryGrid) return;
            
            // Remove only Firebase images
            const firebaseImages = galleryGrid.querySelectorAll('.firebase-image');
            firebaseImages.forEach(img => img.remove());
            
            if (data && Object.keys(data).length > 0) {
                Object.values(data).forEach(image => {
                    const galleryItem = document.createElement('div');
                    galleryItem.className = 'gallery-item firebase-image';
                    galleryItem.innerHTML = `
                        <img src="${this.escapeHtml(image.url)}" 
                             alt="${this.escapeHtml(image.alt || 'صورة من المعرض')}" 
                             class="gallery-image" loading="lazy">
                    `;
                    galleryGrid.appendChild(galleryItem);
                });
                
                console.log(`✅ Loaded ${Object.keys(data).length} gallery images`);
            }
        } catch (error) {
            console.error('❌ Error loading gallery:', error);
        }
    }

    async loadSiteSettings() {
        try {
            const snapshot = await this.database.ref('siteSettings').once('value');
            const data = snapshot.val();

            if (data) {
                // Update email
                if (data.contactEmail) {
                    const emailElement = document.getElementById('contact-email-display');
                    const emailLink = document.getElementById('contact-email-link');
                    if (emailElement) {
                        emailElement.textContent = data.contactEmail;
                    }
                    if (emailLink) {
                        emailLink.href = `mailto:${data.contactEmail}`;
                    }
                }

                // Update phone
                if (data.contactPhone) {
                    const phoneElement = document.getElementById('contact-phone-display');
                    const phoneLink = document.getElementById('contact-phone-link');
                    if (phoneElement) {
                        phoneElement.textContent = data.contactPhone;
                    }
                    if (phoneLink) {
                        phoneLink.href = `tel:${data.contactPhone}`;
                    }
                }

                console.log('✅ Site settings loaded');
            }
        } catch (error) {
            console.error('❌ Error loading site settings:', error);
        }
    }

    showAuthStatus(success) {
        // Update connection status if available
        const statusElement = document.getElementById('sync-status');
        if (statusElement) {
            if (success) {
                statusElement.className = 'sync-status online';
                statusElement.textContent = '🟢 متصل';
            } else {
                statusElement.className = 'sync-status offline';
                statusElement.textContent = '🔴 غير متصل';
            }
        }
    }

    showStaticContent() {
        console.log('📋 Showing no data messages...');

        // Show no data message for branches
        const branchesGrid = document.getElementById('dynamic-branches');
        const noDataMessage = document.getElementById('no-branches-message');
        if (branchesGrid && noDataMessage) {
            noDataMessage.style.display = 'block';
        }

        // Show no data for contact info
        this.showStaticContactInfo();
    }

    showStaticContactInfo() {
        // Update contact elements with no data messages
        const updates = [
            { id: 'contact-title', value: 'تواصل معنا' },
            { id: 'contact-info-title', value: 'معلومات الاتصال' },
            { id: 'contact-address-display', value: 'لا توجد معلومات متاحة' },
            { id: 'contact-hours-display', value: 'لا توجد معلومات متاحة' },
            { id: 'contact-email-display', value: 'لا توجد معلومات متاحة' },
            { id: 'contact-phone-display', value: 'لا توجد معلومات متاحة' }
        ];

        updates.forEach(update => {
            const element = document.getElementById(update.id);
            if (element) {
                element.textContent = update.value;
            }
        });

        // Hide loading spinners
        const loadingElements = ['address-loading', 'hours-loading', 'phone-loading', 'email-loading'];
        loadingElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });

        // Disable contact links when showing static content
        this.disableContactLinks();
    }

    disableContactLinks() {
        const emailLink = document.getElementById('contact-email-link');
        const phoneLink = document.getElementById('contact-phone-link');

        if (emailLink) {
            emailLink.href = '#';
            emailLink.onclick = (e) => {
                e.preventDefault();
                alert('معلومات الاتصال غير متاحة حالياً. يرجى المحاولة لاحقاً.');
            };
        }

        if (phoneLink) {
            phoneLink.href = '#';
            phoneLink.onclick = (e) => {
                e.preventDefault();
                alert('معلومات الاتصال غير متاحة حالياً. يرجى المحاولة لاحقاً.');
            };
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public methods
    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    async forceReload() {
        if (this.isAuthenticated) {
            await this.loadContentDirectly();
        } else {
            await this.init();
        }
    }
}

// Global functions
window.reloadHomepageContent = function() {
    if (window.homepageAuth) {
        window.homepageAuth.forceReload();
    }
};

window.getHomepageAuthStatus = function() {
    if (window.homepageAuth) {
        return {
            isAuthenticated: window.homepageAuth.isUserAuthenticated(),
            user: window.homepageAuth.getCurrentUser()
        };
    }
    return { isAuthenticated: false, user: null };
};

// Initialize Homepage Authentication
function initializeHomepageAuth() {
    if (typeof firebase !== 'undefined') {
        window.homepageAuth = new HomepageAuthManager();
        console.log('✅ Homepage Authentication Manager started');
    } else {
        setTimeout(initializeHomepageAuth, 1000);
    }
}

// Auto-initialize
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initializeHomepageAuth, 500);
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.homepageAuth && window.homepageAuth.auth) {
        // Sign out anonymous users to clean up
        if (window.homepageAuth.currentUser && window.homepageAuth.currentUser.isAnonymous) {
            window.homepageAuth.auth.signOut();
        }
    }
});
