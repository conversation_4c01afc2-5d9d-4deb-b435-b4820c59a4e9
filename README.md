# AL-SALAMAT - موقع شركة السلامات لزجاج السيارات

موقع إلكتروني متكامل لشركة السلامات المتخصصة في خدمات زجاج السيارات.

## 🌟 المميزات

### الميزات الأساسية
- 🎨 تصميم متجاوب مع جميع الأجهزة
- 🔐 نظام تسجيل دخول وإنشاء حساب
- 🏢 عرض الفروع مع معلومات الاتصال
- 📧 نموذج اتصال متصل بقاعدة البيانات
- 🖼️ معرض صور قابل للإدارة

### لوحة التحكم الإدارية
- 👨‍💼 لوحة إدارة شاملة للمديرين
- 📊 إحصائيات ولوحة تحكم
- 🗂️ إدارة المحتوى والفروع
- 👥 إدارة المستخدمين والرسائل
- 🖼️ إدارة معرض الصور
- ⚙️ إعدادات الموقع

## 📁 الملفات الأساسية

- `index.html` - الصفحة الرئيسية
- `admin.html` - لوحة التحكم الإدارية
- `login.html` - صفحة تسجيل الدخول والتسجيل
- `styles.css` - ملف التنسيقات الرئيسي
- `admin-styles.css` - تنسيقات لوحة التحكم
- `realtime-styles.css` - تنسيقات التحديثات المباشرة
- `admin-script.js` - سكريبت لوحة التحكم
- `database-rules.json` - قواعد قاعدة البيانات Firebase

## 🔥 إعداد Firebase

الموقع يستخدم Firebase لـ:
- **المصادقة**: تسجيل الدخول وإنشاء الحسابات
- **قاعدة البيانات**: تخزين المحتوى والبيانات
- **نموذج الاتصال**: استقبال رسائل العملاء

## 👨‍💼 الوصول للإدارة

**البريد الإلكتروني الافتراضي للمدير:** `<EMAIL>`

## 🚀 التشغيل

1. ارفع جميع الملفات إلى الخادم
2. أنشئ مشروع Firebase وفعّل Authentication و Realtime Database
3. استبدل إعدادات Firebase في الملفات
4. ادخل إلى لوحة التحكم لإضافة المحتوى

## 🛠️ التقنيات المستخدمة

- HTML5, CSS3, JavaScript (ES6+)
- Firebase Authentication
- Firebase Realtime Database

## 📞 الدعم الفني

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

---

**تاريخ آخر تحديث:** 2024
**الإصدار:** 1.0
**المطور:** Augment Agent
