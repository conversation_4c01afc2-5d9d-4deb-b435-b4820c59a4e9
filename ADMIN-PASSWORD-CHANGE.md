# ميزة تغيير كلمة سر الأدمن

## نظرة عامة

تم إضافة ميزة تغيير كلمة السر للأدمن في لوحة الإدارة، والتي تسمح للمدير بتغيير كلمة السر الخاصة به بشكل آمن.

## الموقع

تم إضافة قسم تغيير كلمة السر في:
- **القسم**: إدارة المستخدمين
- **الموقع**: `admin.html` → قائمة "👥 إدارة المستخدمين"

## المميزات

### 🔐 الأمان
- **التحقق من الهوية**: يتطلب إدخال كلمة السر الحالية للتأكيد
- **صلاحيات الأدمن فقط**: متاح للمدير فقط
- **تشفير Firebase**: يستخدم نظام Firebase Auth الآمن
- **سجل العمليات**: يتم تسجيل عمليات تغيير كلمة السر

### 📊 مؤشر قوة كلمة السر
- **ضعيف** (أحمر): أقل من 6 أحرف أو بسيط
- **متوسط** (برتقالي): 6-8 أحرف مع تنوع محدود
- **جيد** (أصفر): 8+ أحرف مع تنوع جيد
- **قوي** (أخضر): 8+ أحرف مع أرقام ورموز وأحرف كبيرة وصغيرة

### ✅ التحقق من التطابق
- **تأكيد كلمة السر**: يتحقق من تطابق كلمة السر الجديدة مع التأكيد
- **ألوان بصرية**: أخضر للتطابق، أحمر لعدم التطابق

## كيفية الاستخدام

### 1. الوصول للميزة
1. سجل دخول إلى لوحة الإدارة
2. اذهب إلى قسم "👥 إدارة المستخدمين"
3. ستجد قسم "🔐 تغيير كلمة سر الأدمن" في الأعلى

### 2. تغيير كلمة السر
1. **أدخل كلمة السر الحالية** في الحقل الأول
2. **أدخل كلمة السر الجديدة** (6 أحرف على الأقل)
3. **أكد كلمة السر الجديدة** في الحقل الثالث
4. **راقب مؤشر القوة** للتأكد من قوة كلمة السر
5. **اضغط "🔐 تغيير كلمة السر"**

### 3. رسائل النجاح والخطأ
- ✅ **نجح**: "تم تغيير كلمة السر بنجاح"
- ❌ **كلمة سر خاطئة**: "كلمة السر الحالية غير صحيحة"
- ❌ **عدم تطابق**: "كلمة السر الجديدة وتأكيدها غير متطابقين"
- ❌ **ضعيفة**: "كلمة السر يجب أن تكون 6 أحرف على الأقل"

## الملفات المعدلة

### 1. `admin.html`
```html
<!-- قسم تغيير كلمة السر -->
<div class="admin-card">
    <h3>🔐 تغيير كلمة سر الأدمن</h3>
    <form id="change-password-form">
        <!-- حقول كلمة السر -->
        <!-- مؤشر القوة -->
        <!-- أزرار التحكم -->
    </form>
</div>
```

### 2. `admin-styles.css`
```css
/* تصميم مؤشر قوة كلمة السر */
.password-strength { ... }
.strength-bar { ... }
.strength-weak { color: #dc3545; }
.strength-strong { color: #28a745; }
```

### 3. `admin-script.js`
```javascript
// وظائف تغيير كلمة السر
async changeAdminPassword() { ... }
checkPasswordStrength(password) { ... }
checkPasswordMatch() { ... }
```

## الأمان والحماية

### 🛡️ آليات الحماية
1. **التحقق من الصلاحيات**: فقط المدير يمكنه الوصول
2. **إعادة المصادقة**: يتطلب كلمة السر الحالية
3. **تشفير Firebase**: كلمات السر مشفرة بالكامل
4. **سجل العمليات**: تسجيل جميع محاولات التغيير

### 📝 سجل العمليات
يتم حفظ سجل في Firebase تحت `adminLogs`:
```json
{
  "action": "password_changed",
  "adminEmail": "<EMAIL>",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "ip": "unknown"
}
```

## متطلبات النظام

### ✅ المتطلبات
- **Firebase Auth**: نشط ومُعد
- **حساب أدمن**: موجود ومُسجل دخول
- **متصفح حديث**: يدعم JavaScript ES6+
- **اتصال إنترنت**: للتواصل مع Firebase

### 🔧 الإعدادات المطلوبة
- Firebase Authentication مُفعل
- قواعد Firebase Database تسمح للأدمن بالكتابة
- حساب أدمن بصلاحيات صحيحة

## استكشاف الأخطاء

### ❌ مشاكل شائعة

1. **"ليس لديك صلاحيات"**
   - تأكد من تسجيل الدخول كأدمن
   - تحقق من البريد الإلكتروني: `<EMAIL>`

2. **"كلمة السر الحالية غير صحيحة"**
   - تأكد من إدخال كلمة السر الصحيحة
   - جرب تسجيل الخروج والدخول مرة أخرى

3. **"خطأ في الاتصال"**
   - تحقق من اتصال الإنترنت
   - تأكد من إعدادات Firebase

### 🔍 التشخيص
- افتح Developer Tools (F12)
- تحقق من Console للأخطاء
- راجع Network tab للطلبات المرفوضة

## الأمان الإضافي

### 💡 نصائح لكلمة سر قوية
- **8 أحرف على الأقل**
- **أحرف كبيرة وصغيرة**
- **أرقام ورموز**
- **تجنب المعلومات الشخصية**
- **تغيير دوري**

### 🔄 صيانة دورية
- غير كلمة السر كل 3-6 أشهر
- راجع سجل العمليات بانتظام
- تأكد من عدم مشاركة كلمة السر

---

**تاريخ الإنشاء**: $(date)
**الحالة**: ✅ جاهز للاستخدام
**المطور**: Augment Agent
