// Dynamic Content Management for AL-SALAMAT Website
// This file handles real-time updates from Firebase to the main website

class DynamicContentManager {
    constructor() {
        this.database = null;
        this.listeners = [];
        this.retryCount = 0;
        this.maxRetries = 5;
        this.isOnline = navigator.onLine;
        this.lastUpdateTime = {};
        this.init();
    }

    async init() {
        try {
            console.log('🚀 Dynamic Content Manager initializing...');

            // Show default content immediately
            this.showDefaultContent();

            // Wait for Firebase to be ready
            if (typeof firebase === 'undefined') {
                console.log('🔥 Firebase not loaded, retrying...');
                this.retryCount++;
                if (this.retryCount < this.maxRetries) {
                    setTimeout(() => this.init(), 1000);
                } else {
                    console.error('❌ Firebase failed to load after maximum retries');
                    console.log('📋 Using default content only');
                }
                return;
            }

            this.database = firebase.database();

            // Enable offline persistence and real-time sync
            this.database.goOnline();

            console.log('🚀 Dynamic Content Manager initialized with Firebase');

            // Set up network monitoring
            this.setupNetworkMonitoring();

            // Load Firebase content to update defaults
            await this.loadAllContent();

            // Then set up real-time listeners
            this.setupRealtimeListeners();

            // Set up periodic refresh to ensure data consistency
            this.setupPeriodicRefresh();

        } catch (error) {
            console.error('❌ Error initializing Dynamic Content Manager:', error);
            console.log('📋 Continuing with default content');
        }
    }

    // Set up real-time listeners for automatic updates
    setupRealtimeListeners() {
        // Listen for company info changes
        const companyInfoRef = this.database.ref('siteContent');
        companyInfoRef.on('value', (snapshot) => {
            const data = snapshot.val();
            if (data) {
                this.updateCompanyInfo(data);
            }
        });





        // Listen for contact section changes
        const contactSectionRef = this.database.ref('contactSection');
        contactSectionRef.on('value', (snapshot) => {
            const data = snapshot.val();
            console.log('Contact section data received:', data);
            if (data) {
                this.updateContactSection(data);
            }
        });

        // Listen for about section changes
        const aboutSectionRef = this.database.ref('aboutSection');
        aboutSectionRef.on('value', (snapshot) => {
            const data = snapshot.val();
            console.log('About section data received:', data);
            if (data) {
                this.updateAboutSection(data);
            }
        });

        // Listen for branches changes with immediate updates
        const branchesRef = this.database.ref('branches');
        branchesRef.on('value', (snapshot) => {
            try {
                const data = snapshot.val();
                const timestamp = Date.now();

                // Check if this is a new update to prevent duplicate processing
                if (!this.lastUpdateTime.branches || timestamp - this.lastUpdateTime.branches > 500) {
                    this.lastUpdateTime.branches = timestamp;
                    console.log('🏢 Branches updated:', data);
                    console.log('📊 Number of branches:', data ? Object.keys(data).length : 0);

                    // Immediate update without delay for better responsiveness
                    this.updateBranches(data);
                    // Don't show notification
                }
            } catch (error) {
                console.error('❌ Error in branches listener:', error);
            }
        }, (error) => {
            console.error('❌ Branches listener error:', error);
            this.retryListener('branches', () => this.setupBranchesListener());
        });

        // Listen for gallery changes
        const galleryRef = this.database.ref('gallery');
        galleryRef.on('value', (snapshot) => {
            const galleryData = snapshot.val();
            // Also get galleryImages data
            this.database.ref('galleryImages').once('value').then(galleryImagesSnapshot => {
                const galleryImagesData = galleryImagesSnapshot.val();
                this.updateGallery(galleryData, galleryImagesData);
            });
        });

        // Listen for galleryImages changes (new admin uploads)
        const galleryImagesRef = this.database.ref('galleryImages');
        galleryImagesRef.on('value', (snapshot) => {
            const galleryImagesData = snapshot.val();
            // Also get old gallery data
            this.database.ref('gallery').once('value').then(gallerySnapshot => {
                const galleryData = gallerySnapshot.val();
                this.updateGallery(galleryData, galleryImagesData);
            });
        });

        // Listen for site settings changes
        const settingsRef = this.database.ref('siteSettings');
        settingsRef.on('value', (snapshot) => {
            const data = snapshot.val();
            if (data) {
                this.updateSiteSettings(data);
            }
        });

        // Listen for banner settings changes
        const bannerRef = this.database.ref('bannerSettings');
        bannerRef.on('value', (snapshot) => {
            const data = snapshot.val();
            this.updateBannerSettings(data);
        });

        // Listen for service images changes
        const serviceImagesRef = this.database.ref('serviceImages');
        serviceImagesRef.on('value', (snapshot) => {
            const data = snapshot.val();
            if (data) {
                this.updateServiceImages(data);
            }
        });

        // Listen for hero images changes
        const heroImagesRef = this.database.ref('heroImages');
        heroImagesRef.on('value', (snapshot) => {
            const data = snapshot.val();
            if (data) {
                this.updateHeroImages(data);
            }
        });

        console.log('Real-time listeners set up');
    }

    // Set up network monitoring
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            console.log('🌐 Network connection restored');
            this.isOnline = true;
            this.database.goOnline();
            this.forceRefreshAllContent();
        });

        window.addEventListener('offline', () => {
            console.log('📴 Network connection lost');
            this.isOnline = false;
        });
    }

    // Set up periodic refresh to ensure data consistency
    setupPeriodicRefresh() {
        // Refresh every 30 seconds to ensure data consistency
        setInterval(() => {
            if (this.isOnline) {
                console.log('🔄 Periodic refresh check...');
                this.forceRefreshAllContent();
            }
        }, 30000);
    }

    // Force refresh all content (bypass cache)
    async forceRefreshAllContent() {
        try {
            console.log('🔄 Force refreshing all content...');

            // Clear browser cache for Firebase data
            if (this.database) {
                this.database.goOffline();
                await new Promise(resolve => setTimeout(resolve, 100));
                this.database.goOnline();
            }

            // Reload all content
            await this.loadAllContent();

            console.log('✅ Force refresh completed');
        } catch (error) {
            console.error('❌ Error during force refresh:', error);
        }
    }

    // Show update notification (disabled)
    showUpdateNotification(message) {
        // Don't show notifications
        console.log(`🔔 ${message} (notification disabled)`);
    }

    // Retry failed listeners
    retryListener(section, setupFunction) {
        console.log(`🔄 Retrying listener for ${section}...`);
        setTimeout(() => {
            if (this.isOnline) {
                setupFunction();
            }
        }, 5000);
    }

    // Show default content immediately (before Firebase loads)
    showDefaultContent() {
        console.log('📋 Showing default content immediately...');

        // Hide all loading spinners
        this.hideLoadingSpinners();

        // Ensure default content is visible
        this.ensureDefaultContentVisible();

        console.log('✅ Default content is now visible');
    }

    hideLoadingSpinners() {
        const loadingElements = [
            'about-loading',
            'branches-loading',
            'gallery-loading',
            'address-loading',
            'hours-loading'
        ];

        loadingElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });
    }

    ensureDefaultContentVisible() {
        // Ensure about section is visible
        const aboutDesc = document.getElementById('about-description');
        if (aboutDesc) {
            aboutDesc.classList.remove('hidden');
            aboutDesc.style.display = 'block';
        }

        // Ensure default branches are visible
        const defaultBranches = document.querySelectorAll('.default-branch');
        defaultBranches.forEach(branch => {
            branch.style.display = 'block';
        });

        // Ensure contact info is visible
        const contactElements = [
            'contact-address-display',
            'contact-hours-display',
            'contact-phone-display',
            'contact-email-display'
        ];

        contactElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'inline';
            }
        });
    }

    // Load all content initially
    async loadAllContent() {
        try {
            await Promise.all([
                this.loadCompanyInfo(),
                this.loadContactSection(),
                this.loadAboutSection(),
                this.loadBranches(),
                this.loadGallery(),
                this.loadServiceImages(),
                this.loadHeroImages(),
                this.loadSiteSettings(),
                this.loadBannerSettings()
            ]);
            console.log('All dynamic content loaded from Firebase');
        } catch (error) {
            console.error('Error loading content from Firebase:', error);
            console.log('📋 Continuing with default content');
        }
    }

    // Load company information
    async loadCompanyInfo() {
        try {
            const snapshot = await this.database.ref('siteContent').once('value');
            const data = snapshot.val();
            if (data) {
                this.updateCompanyInfo(data);
            }
        } catch (error) {
            console.error('Error loading company info:', error);
        }
    }

    // Update company information in the UI
    updateCompanyInfo(data) {
        try {
            if (data && data.title) {
                const titleElement = document.getElementById('company-title');
                if (titleElement) {
                    titleElement.textContent = data.title;
                    this.animateUpdate(titleElement);
                }
            }

            if (data && data.subtitle) {
                const subtitleElement = document.getElementById('company-subtitle');
                if (subtitleElement) {
                    subtitleElement.textContent = data.subtitle;
                    this.animateUpdate(subtitleElement);
                }
            }

            if (data && data.description) {
                const descriptionElement = document.getElementById('company-description');
                if (descriptionElement) {
                    descriptionElement.textContent = data.description;
                    this.animateUpdate(descriptionElement);
                }
            }

            console.log('Company info updated');
        } catch (error) {
            console.error('Error updating company info:', error);
        }
    }





    // Load contact section
    async loadContactSection() {
        try {
            const snapshot = await this.database.ref('contactSection').once('value');
            const data = snapshot.val();
            if (data) {
                this.updateContactSection(data);
            }
        } catch (error) {
            console.error('Error loading contact section:', error);
        }
    }

    // Load about section
    async loadAboutSection() {
        try {
            const snapshot = await this.database.ref('aboutSection').once('value');
            const data = snapshot.val();
            if (data) {
                this.updateAboutSection(data);
            }
        } catch (error) {
            console.error('Error loading about section:', error);
        }
    }

    // Update contact section in the UI
    updateContactSection(data) {
        try {
            console.log('Updating contact section with data:', data);

            if (data && data.title) {
                const titleElement = document.getElementById('contact-title');
                console.log('Title element found:', titleElement);
                if (titleElement) {
                    titleElement.textContent = data.title;
                    this.animateUpdate(titleElement);
                    console.log('Title updated to:', data.title);
                }
            }

            if (data && data.infoTitle) {
                const infoTitleElement = document.getElementById('contact-info-title');
                console.log('Info title element found:', infoTitleElement);
                if (infoTitleElement) {
                    infoTitleElement.textContent = data.infoTitle;
                    this.animateUpdate(infoTitleElement);
                    console.log('Info title updated to:', data.infoTitle);
                }
            }

            if (data && data.address) {
                const addressElement = document.getElementById('contact-address-display');
                const addressLoading = document.getElementById('address-loading');
                console.log('Address element found:', addressElement);
                if (addressElement) {
                    // Hide loading spinner
                    if (addressLoading) {
                        addressLoading.style.display = 'none';
                    }
                    addressElement.textContent = data.address;
                    this.animateUpdate(addressElement);
                    console.log('Address updated to:', data.address);
                }
            }

            if (data && data.hours) {
                const hoursElement = document.getElementById('contact-hours-display');
                const hoursLoading = document.getElementById('hours-loading');
                console.log('Hours element found:', hoursElement);
                if (hoursElement) {
                    // Hide loading spinner
                    if (hoursLoading) {
                        hoursLoading.style.display = 'none';
                    }
                    hoursElement.textContent = data.hours;
                    this.animateUpdate(hoursElement);
                    console.log('Hours updated to:', data.hours);
                }
            }

            console.log('Contact section updated successfully');
        } catch (error) {
            console.error('Error updating contact section:', error);
        }
    }

    // Update about section in the UI
    updateAboutSection(data) {
        try {
            console.log('Updating about section with data:', data);

            const loadingElement = document.getElementById('about-loading');
            const titleElement = document.getElementById('about-title');
            const descriptionElement = document.getElementById('about-description');

            if (data && (data.title || data.description)) {
                // Hide loading spinner
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }

                // Update title
                if (data.title && titleElement) {
                    titleElement.textContent = data.title;
                    this.animateUpdate(titleElement);
                    console.log('About title updated to:', data.title);
                }

                // Update description and show it
                if (data.description && descriptionElement) {
                    descriptionElement.textContent = data.description;
                    descriptionElement.classList.remove('hidden');
                    this.animateUpdate(descriptionElement);
                    console.log('About description updated to:', data.description);
                }
            } else {
                // No data available, keep loading spinner visible
                console.log('No about data available, keeping loading spinner');
            }

            console.log('About section updated successfully');
        } catch (error) {
            console.error('Error updating about section:', error);
        }
    }

    // Load branches
    async loadBranches() {
        try {
            console.log('🔄 Loading branches from Firebase...');
            const snapshot = await this.database.ref('branches').once('value');
            const data = snapshot.val();
            console.log('📥 Branches loaded:', data);
            console.log('📊 Number of branches loaded:', data ? Object.keys(data).length : 0);

            // Force update branches
            this.updateBranches(data);
        } catch (error) {
            console.error('❌ Error loading branches:', error);
        }
    }

    // Update branches in the UI with enhanced reliability
    updateBranches(branchesData) {
        try {
            console.log('🏢 Updating branches with data:', branchesData);
            const branchesGrid = document.getElementById('dynamic-branches');
            const noDataMessage = document.getElementById('no-branches-message');
            const branchesLoading = document.getElementById('branches-loading');

            console.log('📍 Branches grid element:', branchesGrid);
            console.log('📝 No data message element:', noDataMessage);

            if (!branchesGrid) {
                console.error('❌ Branches grid element not found!');
                return;
            }

            // Hide loading spinner
            if (branchesLoading) {
                branchesLoading.style.display = 'none';
            }

            // Clear existing Firebase branches only (keep default branches)
            const existingFirebaseBranches = branchesGrid.querySelectorAll('.branch-card:not(.default-branch)');
            existingFirebaseBranches.forEach(branch => branch.remove());

            if (branchesData && Object.keys(branchesData).length > 0) {
                console.log('✅ Found Firebase branches data, updating UI...');

                // Hide default branches when we have Firebase data
                const defaultBranches = branchesGrid.querySelectorAll('.default-branch');
                defaultBranches.forEach(branch => {
                    branch.style.display = 'none';
                });

                // Hide no data message
                if (noDataMessage) {
                    noDataMessage.style.display = 'none';
                    noDataMessage.classList.add('hidden');
                    console.log('🙈 Hidden no data message - Firebase branches available');
                }

                // Add Firebase branches with improved error handling
                const branchEntries = Object.entries(branchesData);
                console.log(`➕ Adding ${branchEntries.length} Firebase branches...`);

                branchEntries.forEach(([branchId, branch], index) => {
                    try {
                        console.log(`📍 Adding branch ${index + 1}:`, branch);

                        // Validate branch data
                        if (!branch || typeof branch !== 'object') {
                            console.warn(`⚠️ Invalid branch data for ${branchId}:`, branch);
                            return;
                        }

                        const branchCard = document.createElement('div');
                        branchCard.className = 'branch-card firebase-branch';
                        branchCard.setAttribute('data-branch-id', branchId);

                        // Ensure all required fields exist with fallbacks
                        const branchName = this.escapeHtml(branch.name || 'فرع غير محدد');
                        const branchAddress = this.escapeHtml(branch.address || 'عنوان غير محدد');
                        const branchPhone = this.escapeHtml(branch.phone || '');

                        // Create branch card HTML
                        branchCard.innerHTML = `
                            <h3>${branchName}</h3>
                            <p><strong>العنوان:</strong> ${branchAddress}</p>
                            ${branchPhone ? `<p><strong>الهاتف:</strong> ${branchPhone}</p>` : ''}
                            <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || branchName)}"
                               target="_blank"
                               class="location-btn">
                               📍 الموقع
                            </a>
                        `;

                        // Add animation class
                        branchCard.classList.add('fade-in');

                        branchesGrid.appendChild(branchCard);
                        console.log(`✅ Branch ${index + 1} added successfully: ${branchName}`);
                    } catch (branchError) {
                        console.error(`❌ Error adding branch ${index + 1}:`, branchError);
                    }
                });

                // Force DOM reflow and animate
                branchesGrid.offsetHeight;
                this.animateUpdate(branchesGrid);

                console.log('🎉 Branches updated successfully!');

                // Final verification
                const finalCards = branchesGrid.querySelectorAll('.branch-card');
                console.log(`✅ Final verification: ${finalCards.length} branch cards in DOM`);

            } else {
                console.log('⚠️ No Firebase branches data, keeping default branches visible...');

                // Ensure default branches are visible when no Firebase data
                const defaultBranches = branchesGrid.querySelectorAll('.default-branch');
                defaultBranches.forEach(branch => {
                    branch.style.display = 'block';
                });

                // Hide no data message since we have default branches
                if (noDataMessage) {
                    noDataMessage.style.display = 'none';
                    noDataMessage.classList.add('hidden');
                }

                console.log('ℹ️ No Firebase branches data - showing default branches');
            }
        } catch (error) {
            console.error('❌ Error updating branches:', error);
            console.error('Error details:', error.stack);

            // Fallback error display
            const branchesGrid = document.getElementById('dynamic-branches');
            if (branchesGrid) {
                branchesGrid.innerHTML = '<div class="error-message"><p>خطأ في تحميل الفروع. يرجى المحاولة مرة أخرى.</p></div>';
            }
        }
    }

    // Load gallery
    async loadGallery() {
        try {
            // Load both old gallery and new galleryImages
            const [gallerySnapshot, galleryImagesSnapshot] = await Promise.all([
                this.database.ref('gallery').once('value'),
                this.database.ref('galleryImages').once('value')
            ]);

            const galleryData = gallerySnapshot.val();
            const galleryImagesData = galleryImagesSnapshot.val();

            this.updateGallery(galleryData, galleryImagesData);
        } catch (error) {
            console.error('Error loading gallery:', error);
        }
    }

    // Update gallery in the UI
    updateGallery(galleryData, galleryImagesData) {
        try {
            const galleryGrid = document.getElementById('main-gallery-grid');
            const loadingElement = document.getElementById('gallery-loading');

            if (!galleryGrid) return;

            // Hide loading spinner
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            // Clear existing Firebase images
            const firebaseImages = galleryGrid.querySelectorAll('.firebase-gallery-image');
            firebaseImages.forEach(img => img.remove());

            let hasImages = false;

            // Add images from galleryImages (new admin uploads)
            if (galleryImagesData && Object.keys(galleryImagesData).length > 0) {
                const images = Object.entries(galleryImagesData)
                    .sort((a, b) => {
                        // Sort featured images first, then by upload date
                        if (a[1].featured && !b[1].featured) return -1;
                        if (!a[1].featured && b[1].featured) return 1;
                        return new Date(b[1].uploadedAt) - new Date(a[1].uploadedAt);
                    });

                images.forEach(([id, image]) => {
                    const imageElement = this.createMainGalleryItem(image);
                    galleryGrid.appendChild(imageElement);
                    hasImages = true;
                });
            }

            // Add images from old gallery system if exists
            if (galleryData && Object.keys(galleryData).length > 0) {
                Object.entries(galleryData).forEach(([id, image]) => {
                    const imageElement = this.createMainGalleryItem(image);
                    galleryGrid.appendChild(imageElement);
                    hasImages = true;
                });
            }

            // Show message if no images
            if (!hasImages) {
                galleryGrid.innerHTML = '<div class="no-gallery-message"><p>لا توجد صور في المعرض حالياً</p></div>';
            } else {
                this.animateUpdate(galleryGrid);
            }

            console.log('Gallery updated with', hasImages ? 'images' : 'no images');
        } catch (error) {
            console.error('Error updating gallery:', error);
        }
    }

    createMainGalleryItem(image) {
        const div = document.createElement('div');
        div.className = 'gallery-main-item firebase-gallery-image';
        div.setAttribute('data-category', image.category || 'general');

        const categoryName = this.getCategoryDisplayName(image.category);

        div.innerHTML = `
            <div class="gallery-image-container">
                <img src="${image.url}" alt="${image.title}" class="gallery-main-image">
                ${image.featured ? '<div class="featured-badge">مميزة</div>' : ''}
            </div>
            <div class="gallery-item-info">
                <h4>${image.title}</h4>
                <p>${image.description}</p>
                <div class="gallery-item-meta">
                    <span class="category-badge">${categoryName}</span>
                </div>
                <div class="gallery-item-actions">
                    <span class="view-icon">👁️</span>
                    <span class="view-text">عرض التفاصيل</span>
                </div>
            </div>
        `;

        // Add click event to open modal
        div.addEventListener('click', () => {
            this.openImageModal(image.url, image.title, image.description);
        });

        return div;
    }

    getCategoryDisplayName(category) {
        const categories = {
            'services': 'خدماتنا',
            'installation': 'التركيب',
            'repair': 'الإصلاح',
            'products': 'منتجاتنا'
        };
        return categories[category] || 'عام';
    }

    openImageModal(imageUrl, title, description) {
        // Use existing modal function if available
        if (typeof openImageModal === 'function') {
            openImageModal(imageUrl, title, description);
        }
    }

    // Load site settings
    async loadSiteSettings() {
        try {
            const snapshot = await this.database.ref('siteSettings').once('value');
            const data = snapshot.val();
            if (data) {
                this.updateSiteSettings(data);
            }
        } catch (error) {
            console.error('Error loading site settings:', error);
        }
    }

    // Update site settings in the UI
    updateSiteSettings(settings) {
        try {
            if (settings && settings.contactEmail) {
                const emailElement = document.getElementById('contact-email-display');
                if (emailElement) {
                    emailElement.textContent = settings.contactEmail;
                    this.animateUpdate(emailElement);
                }

                // Update email link
                const emailLink = document.getElementById('contact-email-link');
                if (emailLink) {
                    emailLink.href = `mailto:${settings.contactEmail}`;
                    emailLink.onclick = null; // Remove any disabled onclick handlers
                }
            }

            if (settings && settings.contactPhone) {
                const phoneElement = document.getElementById('contact-phone-display');
                if (phoneElement) {
                    phoneElement.textContent = settings.contactPhone;
                    this.animateUpdate(phoneElement);
                }

                // Update phone link
                const phoneLink = document.getElementById('contact-phone-link');
                if (phoneLink) {
                    phoneLink.href = `tel:${settings.contactPhone}`;
                    phoneLink.onclick = null; // Remove any disabled onclick handlers
                }
            }

            console.log('Site settings updated');
        } catch (error) {
            console.error('Error updating site settings:', error);
        }
    }

    // Animate element update
    animateUpdate(element) {
        if (!element) return;

        // Show update indicator
        this.showUpdateIndicator();

        element.style.transition = 'all 0.3s ease';
        element.style.transform = 'scale(1.02)';
        element.style.boxShadow = '0 0 10px rgba(102, 126, 234, 0.3)';

        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.boxShadow = '';
        }, 300);
    }

    // Show update indicator (disabled)
    showUpdateIndicator() {
        // Don't show update indicator
    }

    // Load banner settings
    async loadBannerSettings() {
        try {
            const snapshot = await this.database.ref('bannerSettings').once('value');
            const data = snapshot.val();
            this.updateBannerSettings(data);
        } catch (error) {
            console.error('Error loading banner settings:', error);
        }
    }

    // Update banner settings in the UI
    updateBannerSettings(data) {
        try {
            const bannerElement = document.querySelector('.moving-text-banner');
            const textElements = document.querySelectorAll('.moving-text span');

            if (!bannerElement) return;

            // Always show banner by default (enabled unless explicitly disabled)
            if (!data || data.enabled !== false) {
                // Show banner with force
                bannerElement.style.display = 'flex';
                bannerElement.style.visibility = 'visible';
                bannerElement.style.opacity = '1';
                bannerElement.classList.remove('hidden');

                // Update text if provided, otherwise use default
                const bannerText = (data && data.text) ? data.text : 'السلامات لزجاج السيارات - متخصصون في تبديل وتركيب زجاج السيارات';

                if (textElements.length > 0) {
                    textElements.forEach(span => {
                        span.textContent = bannerText;
                        span.style.visibility = 'visible';
                        span.style.opacity = '1';
                    });
                    this.animateUpdate(bannerElement);
                }
            } else {
                // Hide banner only if explicitly disabled
                bannerElement.style.display = 'none';
            }

            console.log('Banner settings updated');
        } catch (error) {
            console.error('Error updating banner settings:', error);
        }
    }

    // Escape HTML to prevent XSS
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Load service images
    async loadServiceImages() {
        try {
            const snapshot = await this.database.ref('serviceImages').once('value');
            const data = snapshot.val();

            if (data) {
                // Update service images and text in the gallery section
                this.updateServiceImages(data);
            }
        } catch (error) {
            console.error('Error loading service images:', error);
        }
    }

    // Load hero images (main company images)
    async loadHeroImages() {
        try {
            const snapshot = await this.database.ref('heroImages').once('value');
            const data = snapshot.val();

            if (data) {
                // Update hero images in the main banner/carousel
                this.updateHeroImages(data);
            }
        } catch (error) {
            console.error('Error loading hero images:', error);
        }
    }

    getServiceTitle(serviceType) {
        const serviceTitles = {
            'car2': 'خدمات زجاج السيارات',
            'car4': 'تركيب زجاج',
            'car6': 'إصلاح زجاج'
        };
        return serviceTitles[serviceType] || serviceType;
    }

    // Update hero images in real-time
    updateHeroImages(data) {
        try {
            Object.entries(data).forEach(([imageType, imageData]) => {
                if (imageData.url) {
                    // Find hero image by type (main1, etc.)
                    let heroImage = document.querySelector(`img[src*="${imageType}"]`);

                    // If not found by src, try finding by data attribute or class
                    if (!heroImage) {
                        heroImage = document.querySelector(`[data-image="${imageType}"] img`);
                    }

                    // If still not found, try finding in hero section
                    if (!heroImage) {
                        const heroSection = document.querySelector('.hero, .banner, .main-banner');
                        if (heroSection) {
                            const images = heroSection.querySelectorAll('img');
                            // For main1, usually it's the first or main image
                            if (imageType === 'main1' && images.length > 0) {
                                heroImage = images[0];
                            }
                        }
                    }

                    if (heroImage) {
                        // Handle both Base64 and regular URLs
                        heroImage.src = imageData.url;
                        this.animateUpdate(heroImage);
                        console.log(`Updated hero image ${imageType} with ${imageData.url.startsWith('data:') ? 'Base64' : 'URL'} data`);
                    } else {
                        console.warn(`Hero image element not found for ${imageType}`);
                    }
                }
            });
            console.log('Hero images updated');
        } catch (error) {
            console.error('Error updating hero images:', error);
        }
    }

    // Update service images in real-time
    updateServiceImages(data) {
        try {
            Object.entries(data).forEach(([serviceType, imageData]) => {
                // Find service item by service type - try multiple selectors
                let serviceItem = document.querySelector(`[data-title*="${this.getServiceTitle(serviceType)}"]`);

                // If not found, try finding by image src containing serviceType
                if (!serviceItem) {
                    serviceItem = document.querySelector(`img[src*="${serviceType}"]`)?.closest('.gallery-item');
                }

                // If still not found, try finding by alt text
                if (!serviceItem) {
                    const images = document.querySelectorAll('.gallery-item img');
                    for (let img of images) {
                        if (img.alt && img.alt.includes(this.getServiceTitle(serviceType))) {
                            serviceItem = img.closest('.gallery-item');
                            break;
                        }
                    }
                }

                if (serviceItem) {
                    // Update image if URL exists (now supports Base64)
                    if (imageData.url) {
                        const serviceImage = serviceItem.querySelector('img');
                        if (serviceImage) {
                            // Handle both Base64 and regular URLs
                            serviceImage.src = imageData.url;
                            this.animateUpdate(serviceImage);
                            console.log(`Updated image for ${serviceType} with ${imageData.url.startsWith('data:') ? 'Base64' : 'URL'} data`);
                        }
                    }

                    // Update title and description
                    const titleElement = serviceItem.querySelector('h3');
                    const descElement = serviceItem.querySelector('p');

                    if (imageData.title && titleElement) {
                        titleElement.textContent = imageData.title;
                        this.animateUpdate(titleElement);
                        console.log(`Updated title for ${serviceType}: ${imageData.title}`);
                    }

                    if (imageData.description && descElement) {
                        descElement.textContent = imageData.description;
                        this.animateUpdate(descElement);
                        console.log(`Updated description for ${serviceType}: ${imageData.description}`);
                    }

                    // Update data-title attribute for future searches
                    if (imageData.title) {
                        serviceItem.setAttribute('data-title', imageData.title);
                    }
                } else {
                    console.warn(`Service item not found for ${serviceType}`);
                }
            });
            console.log('Service images and text updated');
        } catch (error) {
            console.error('Error updating service images:', error);
        }
    }

    // Load static content as fallback when Firebase is not available
    loadStaticContent() {
        console.log('📋 Loading static content as fallback...');

        // Show static branches
        this.showStaticBranches();

        // Show static contact info
        this.showStaticContactInfo();

        // Show static about section
        this.showStaticAboutSection();

        console.log('✅ Static content loaded');
    }

    showStaticBranches() {
        const branchesGrid = document.getElementById('dynamic-branches');
        const noDataMessage = document.getElementById('no-branches-message');
        const branchesLoading = document.getElementById('branches-loading');

        // Hide loading spinner
        if (branchesLoading) {
            branchesLoading.style.display = 'none';
        }

        if (branchesGrid) {
            // Clear existing content except loading and no-data elements
            const existingBranches = branchesGrid.querySelectorAll('.branch-card');
            existingBranches.forEach(branch => branch.remove());

            // Add static branch
            const branchCard = document.createElement('div');
            branchCard.className = 'branch-card';
            branchCard.innerHTML = `
                <h3>الفرع الرئيسي</h3>
                <p><strong>العنوان:</strong> الرياض، المملكة العربية السعودية</p>
                <p><strong>الهاتف:</strong> +966 11 123 4567</p>
                <a href="https://maps.google.com/?q=الرياض" target="_blank" class="location-btn">📍 الموقع</a>
            `;
            branchesGrid.appendChild(branchCard);

            if (noDataMessage) {
                noDataMessage.style.display = 'none';
            }
        }
    }

    showStaticContactInfo() {
        // Hide loading spinners
        const addressLoading = document.getElementById('address-loading');
        const hoursLoading = document.getElementById('hours-loading');

        if (addressLoading) {
            addressLoading.style.display = 'none';
        }
        if (hoursLoading) {
            hoursLoading.style.display = 'none';
        }

        // Update contact elements with default values
        const updates = [
            { id: 'contact-title', value: 'تواصل معنا' },
            { id: 'contact-info-title', value: 'معلومات الاتصال' },
            { id: 'contact-address-display', value: 'الرياض، المملكة العربية السعودية' },
            { id: 'contact-hours-display', value: 'السبت - الخميس: 8:00 ص - 6:00 م' },
            { id: 'contact-email-display', value: '<EMAIL>' },
            { id: 'contact-phone-display', value: '+966 11 123 4567' }
        ];

        updates.forEach(update => {
            const element = document.getElementById(update.id);
            if (element) {
                element.textContent = update.value;
            }
        });

        // Enable contact links with static data
        const emailLink = document.getElementById('contact-email-link');
        const phoneLink = document.getElementById('contact-phone-link');

        if (emailLink) {
            emailLink.href = 'mailto:<EMAIL>';
            emailLink.onclick = null; // Remove any disabled onclick handlers
        }

        if (phoneLink) {
            phoneLink.href = 'tel:+966111234567';
            phoneLink.onclick = null; // Remove any disabled onclick handlers
        }
    }

    showStaticAboutSection() {
        const loadingElement = document.getElementById('about-loading');
        const titleElement = document.getElementById('about-title');
        const descriptionElement = document.getElementById('about-description');

        // Hide loading spinner
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Update title
        if (titleElement) {
            titleElement.textContent = 'من نحن';
        }

        // Update description and show it
        if (descriptionElement) {
            descriptionElement.textContent = 'شركة السلامات لزجاج السيارات - رائدة في مجال تركيب وإصلاح زجاج السيارات بأعلى معايير الجودة والاحترافية.';
            descriptionElement.classList.remove('hidden');
        }
    }

    // Clean up listeners
    destroy() {
        this.listeners.forEach(listener => {
            if (listener.off) {
                listener.off();
            }
        });
        this.listeners = [];
        console.log('Dynamic Content Manager destroyed');
    }
}

// Initialize when DOM is ready
// Initialize Dynamic Content Manager when DOM and Firebase are ready
function initializeDynamicContent() {
    console.log('🚀 Initializing Dynamic Content Manager...');
    try {
        if (typeof firebase !== 'undefined') {
            window.dynamicContentManager = new DynamicContentManager();
            console.log('✅ Dynamic Content Manager initialized successfully');
        } else {
            console.log('⏳ Firebase not ready yet, retrying...');
            setTimeout(initializeDynamicContent, 1000);
        }
    } catch (error) {
        console.error('❌ Error initializing Dynamic Content Manager:', error);
        // Retry after error
        setTimeout(initializeDynamicContent, 2000);
    }
}

// Start initialization when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM loaded, starting Dynamic Content Manager initialization...');
    // Give Firebase a moment to load
    setTimeout(initializeDynamicContent, 1000);
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (window.dynamicContentManager) {
        window.dynamicContentManager.destroy();
    }
});
