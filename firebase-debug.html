<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Debug - AL-SALAMAT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔥 Firebase Debug Tool</h1>
        <p>أداة تشخيص مشاكل Firebase في موقع السلامات</p>

        <div class="test-section info">
            <h3>📊 حالة Firebase</h3>
            <div id="firebase-status">جاري فحص Firebase...</div>
            <button onclick="checkFirebaseStatus()">فحص Firebase</button>
        </div>

        <div class="test-section">
            <h3>🔗 اختبار الاتصال</h3>
            <div id="connection-status">لم يتم الاختبار بعد</div>
            <button onclick="testConnection()">اختبار الاتصال</button>
        </div>

        <div class="test-section">
            <h3>📖 اختبار قراءة البيانات</h3>
            <div id="data-test">لم يتم الاختبار بعد</div>
            <button onclick="testDataReading()">اختبار قراءة البيانات</button>
        </div>

        <div class="test-section">
            <h3>🏢 اختبار الفروع</h3>
            <div id="branches-test">لم يتم الاختبار بعد</div>
            <button onclick="testBranches()">اختبار الفروع</button>
        </div>

        <div class="test-section">
            <h3>📝 سجل الأخطاء</h3>
            <div id="error-log" class="log">لا توجد أخطاء حتى الآن...</div>
            <button onclick="clearLog()">مسح السجل</button>
        </div>

        <div class="test-section">
            <h3>🛠️ أدوات الإصلاح</h3>
            <button onclick="reinitializeFirebase()">إعادة تهيئة Firebase</button>
            <button onclick="forceReload()">إعادة تحميل الصفحة</button>
            <button onclick="testManagers()">اختبار المدراء</button>
        </div>
    </div>

    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        let database = null;
        let errorLog = [];

        // Initialize Firebase
        try {
            firebase.initializeApp(firebaseConfig);
            database = firebase.database();
            log('✅ Firebase initialized successfully');
        } catch (error) {
            log('❌ Firebase initialization failed: ' + error.message);
        }

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            errorLog.push(logMessage);
            document.getElementById('error-log').textContent = errorLog.join('\n');
            console.log(logMessage);
        }

        function clearLog() {
            errorLog = [];
            document.getElementById('error-log').textContent = 'تم مسح السجل...';
        }

        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-section ${type}`;
        }

        function checkFirebaseStatus() {
            log('🔍 فحص حالة Firebase...');
            
            if (typeof firebase === 'undefined') {
                setStatus('firebase-status', '❌ Firebase غير محمل', 'error');
                log('❌ Firebase is not loaded');
                return;
            }

            if (!firebase.apps || firebase.apps.length === 0) {
                setStatus('firebase-status', '❌ Firebase غير مهيأ', 'error');
                log('❌ Firebase is not initialized');
                return;
            }

            setStatus('firebase-status', '✅ Firebase محمل ومهيأ بنجاح', 'success');
            log('✅ Firebase is loaded and initialized');
        }

        async function testConnection() {
            log('🔗 اختبار الاتصال بـ Firebase...');
            
            if (!database) {
                setStatus('connection-status', '❌ قاعدة البيانات غير متاحة', 'error');
                log('❌ Database not available');
                return;
            }

            try {
                const snapshot = await database.ref('.info/connected').once('value');
                const connected = snapshot.val();
                
                if (connected) {
                    setStatus('connection-status', '✅ متصل بـ Firebase بنجاح', 'success');
                    log('✅ Connected to Firebase successfully');
                } else {
                    setStatus('connection-status', '⚠️ غير متصل بـ Firebase', 'warning');
                    log('⚠️ Not connected to Firebase');
                }
            } catch (error) {
                setStatus('connection-status', '❌ خطأ في الاتصال: ' + error.message, 'error');
                log('❌ Connection error: ' + error.message);
            }
        }

        async function testDataReading() {
            log('📖 اختبار قراءة البيانات...');
            
            if (!database) {
                setStatus('data-test', '❌ قاعدة البيانات غير متاحة', 'error');
                return;
            }

            try {
                // Test reading different data paths
                const tests = [
                    { path: 'siteSettings', name: 'إعدادات الموقع' },
                    { path: 'aboutSection', name: 'قسم من نحن' },
                    { path: 'branches', name: 'الفروع' },
                    { path: 'contactSection', name: 'قسم الاتصال' }
                ];

                let results = [];
                
                for (const test of tests) {
                    try {
                        const snapshot = await database.ref(test.path).once('value');
                        const data = snapshot.val();
                        
                        if (data) {
                            results.push(`✅ ${test.name}: موجود`);
                            log(`✅ ${test.name} data found`);
                        } else {
                            results.push(`⚠️ ${test.name}: فارغ`);
                            log(`⚠️ ${test.name} data is empty`);
                        }
                    } catch (error) {
                        results.push(`❌ ${test.name}: خطأ`);
                        log(`❌ Error reading ${test.name}: ${error.message}`);
                    }
                }

                setStatus('data-test', results.join('\n'), 'info');
                
            } catch (error) {
                setStatus('data-test', '❌ خطأ في قراءة البيانات: ' + error.message, 'error');
                log('❌ Data reading error: ' + error.message);
            }
        }

        async function testBranches() {
            log('🏢 اختبار بيانات الفروع...');
            
            if (!database) {
                setStatus('branches-test', '❌ قاعدة البيانات غير متاحة', 'error');
                return;
            }

            try {
                const snapshot = await database.ref('branches').once('value');
                const data = snapshot.val();
                
                if (data) {
                    const branchCount = Object.keys(data).length;
                    const branchNames = Object.values(data).map(b => b.name || 'بدون اسم');
                    
                    setStatus('branches-test', 
                        `✅ تم العثور على ${branchCount} فرع:\n${branchNames.join(', ')}`, 
                        'success');
                    log(`✅ Found ${branchCount} branches: ${branchNames.join(', ')}`);
                } else {
                    setStatus('branches-test', '⚠️ لا توجد فروع في قاعدة البيانات', 'warning');
                    log('⚠️ No branches found in database');
                }
            } catch (error) {
                setStatus('branches-test', '❌ خطأ في قراءة الفروع: ' + error.message, 'error');
                log('❌ Branches reading error: ' + error.message);
            }
        }

        function reinitializeFirebase() {
            log('🔄 إعادة تهيئة Firebase...');
            
            try {
                if (firebase.apps.length > 0) {
                    firebase.app().delete();
                }
                
                firebase.initializeApp(firebaseConfig);
                database = firebase.database();
                
                log('✅ تم إعادة تهيئة Firebase بنجاح');
                checkFirebaseStatus();
            } catch (error) {
                log('❌ فشل في إعادة تهيئة Firebase: ' + error.message);
            }
        }

        function forceReload() {
            log('🔄 إعادة تحميل الصفحة...');
            window.location.reload();
        }

        function testManagers() {
            log('🛠️ اختبار مدراء المحتوى...');
            
            // Test if managers are loaded
            const tests = [
                { name: 'HomepageAuthManager', exists: typeof HomepageAuthManager !== 'undefined' },
                { name: 'DynamicContentManager', exists: typeof DynamicContentManager !== 'undefined' },
                { name: 'homepageAuth instance', exists: typeof window.homepageAuth !== 'undefined' },
                { name: 'dynamicContentManager instance', exists: typeof window.dynamicContentManager !== 'undefined' }
            ];

            const results = tests.map(test => 
                test.exists ? `✅ ${test.name}: موجود` : `❌ ${test.name}: مفقود`
            );

            log('Manager test results:\n' + results.join('\n'));
        }

        // Auto-run initial checks
        setTimeout(() => {
            checkFirebaseStatus();
            testConnection();
        }, 1000);
    </script>
</body>
</html>
