<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رسائل الخطأ - AL-SALAMAT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .error-demo {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
        .error-code {
            font-family: monospace;
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚨 اختبار رسائل الخطأ المحسنة</h1>
        <p>هذا الاختبار يتحقق من أن رسائل الخطأ تظهر باللغة العربية بدلاً من رسائل Firebase التقنية.</p>
        
        <div id="test-results">
            <div class="test-result info">
                <strong>📋 حالة الاختبار:</strong> جاري التحضير...
            </div>
        </div>
        
        <div>
            <button class="test-button" onclick="testErrorTranslation()">🔍 اختبار ترجمة الأخطاء</button>
            <button class="test-button" onclick="showErrorExamples()">📋 عرض أمثلة الأخطاء</button>
            <button class="test-button" onclick="testPasswordValidation()">✅ اختبار التحقق</button>
            <button class="test-button" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
        
        <div class="error-demo">
            <h3>🎯 الأخطاء المحسنة</h3>
            <p><strong>قبل الإصلاح:</strong></p>
            <div class="error-code">Firebase: The supplied auth credential is incorrect, malformed or has expired. (auth/invalid-credential)</div>
            
            <p><strong>بعد الإصلاح:</strong></p>
            <div style="color: #dc3545; font-weight: bold;">كلمة السر الحالية غير صحيحة</div>
        </div>
        
        <div class="error-demo">
            <h3>📝 قائمة الأخطاء المترجمة</h3>
            <ul>
                <li><span class="error-code">auth/invalid-credential</span> → "كلمة السر الحالية غير صحيحة"</li>
                <li><span class="error-code">auth/wrong-password</span> → "كلمة السر الحالية غير صحيحة"</li>
                <li><span class="error-code">auth/weak-password</span> → "كلمة السر الجديدة ضعيفة جداً"</li>
                <li><span class="error-code">auth/requires-recent-login</span> → "يرجى تسجيل الخروج والدخول مرة أخرى ثم المحاولة"</li>
                <li><span class="error-code">auth/user-not-found</span> → "المستخدم غير موجود"</li>
                <li><span class="error-code">auth/network-request-failed</span> → "خطأ في الاتصال بالإنترنت"</li>
                <li><span class="error-code">auth/too-many-requests</span> → "تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً"</li>
            </ul>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = `
                <div class="test-result info">
                    <strong>📋 حالة الاختبار:</strong> تم مسح النتائج
                </div>
            `;
        }

        function testErrorTranslation() {
            addResult('🔍 اختبار ترجمة رسائل الخطأ...', 'info');
            
            // Test if the translatePasswordError function exists
            fetch('admin-script.js')
                .then(response => response.text())
                .then(js => {
                    if (js.includes('translatePasswordError')) {
                        addResult('✅ دالة ترجمة الأخطاء موجودة', 'success');
                    } else {
                        addResult('❌ دالة ترجمة الأخطاء مفقودة', 'error');
                    }
                    
                    if (js.includes('auth/invalid-credential')) {
                        addResult('✅ معالجة خطأ invalid-credential موجودة', 'success');
                    } else {
                        addResult('❌ معالجة خطأ invalid-credential مفقودة', 'error');
                    }
                    
                    if (js.includes('كلمة السر الحالية غير صحيحة')) {
                        addResult('✅ الرسالة العربية موجودة', 'success');
                    } else {
                        addResult('❌ الرسالة العربية مفقودة', 'error');
                    }
                    
                    if (js.includes('userFriendlyMessage')) {
                        addResult('✅ استخدام الرسائل المحسنة موجود', 'success');
                    } else {
                        addResult('❌ استخدام الرسائل المحسنة مفقود', 'error');
                    }
                })
                .catch(error => {
                    addResult('❌ خطأ في تحميل admin-script.js: ' + error.message, 'error');
                });
        }

        function showErrorExamples() {
            addResult('📋 عرض أمثلة رسائل الخطأ...', 'info');
            
            const errorExamples = [
                { code: 'auth/invalid-credential', message: 'كلمة السر الحالية غير صحيحة' },
                { code: 'auth/wrong-password', message: 'كلمة السر الحالية غير صحيحة' },
                { code: 'auth/weak-password', message: 'كلمة السر الجديدة ضعيفة جداً' },
                { code: 'auth/requires-recent-login', message: 'يرجى تسجيل الخروج والدخول مرة أخرى ثم المحاولة' },
                { code: 'auth/user-not-found', message: 'المستخدم غير موجود' },
                { code: 'auth/network-request-failed', message: 'خطأ في الاتصال بالإنترنت' },
                { code: 'auth/too-many-requests', message: 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً' }
            ];
            
            errorExamples.forEach(example => {
                addResult(`🔸 ${example.code} → "${example.message}"`, 'info');
            });
            
            addResult('✅ تم عرض جميع أمثلة الأخطاء المترجمة', 'success');
        }

        function testPasswordValidation() {
            addResult('✅ اختبار التحقق من كلمة السر...', 'info');
            
            fetch('admin-script.js')
                .then(response => response.text())
                .then(js => {
                    // Test validation logic
                    if (js.includes('newPassword !== confirmPassword')) {
                        addResult('✅ التحقق من تطابق كلمة السر موجود', 'success');
                    } else {
                        addResult('❌ التحقق من تطابق كلمة السر مفقود', 'error');
                    }
                    
                    if (js.includes('newPassword.length < 6')) {
                        addResult('✅ التحقق من طول كلمة السر موجود', 'success');
                    } else {
                        addResult('❌ التحقق من طول كلمة السر مفقود', 'error');
                    }
                    
                    if (js.includes('verifyAdminAccess')) {
                        addResult('✅ التحقق من صلاحيات الأدمن موجود', 'success');
                    } else {
                        addResult('❌ التحقق من صلاحيات الأدمن مفقود', 'error');
                    }
                    
                    if (js.includes('reauthenticateWithCredential')) {
                        addResult('✅ إعادة المصادقة موجودة', 'success');
                    } else {
                        addResult('❌ إعادة المصادقة مفقودة', 'error');
                    }
                })
                .catch(error => {
                    addResult('❌ خطأ في فحص التحقق: ' + error.message, 'error');
                });
        }

        // Auto-run message when page loads
        window.onload = function() {
            addResult('🎯 مرحباً! تم إصلاح رسائل الخطأ لتظهر باللغة العربية', 'info');
            addResult('🔧 الآن عند إدخال كلمة سر خاطئة ستظهر: "كلمة السر الحالية غير صحيحة"', 'success');
            addResult('📋 اضغط على الأزرار أعلاه لاختبار التحسينات', 'info');
        };
    </script>
</body>
</html>
